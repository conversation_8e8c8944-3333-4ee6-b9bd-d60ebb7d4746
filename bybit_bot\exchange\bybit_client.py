"""
Bybit API Client for Trading Bot
Handles all interactions with Bybit exchange API
"""

import asyncio
import hmac
import hashlib
import json
import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urlencode

import aiohttp
from pybit.unified_trading import HTTP, WebSocket

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger

logger = logging.getLogger("bybit_trading_bot.bybit_client")


class BybitClient:
    """
    Comprehensive Bybit API client with real-time data and trading capabilities
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger(config)
        
        # API endpoints - handle both config patterns
        self.testnet = getattr(config, 'bybit_testnet', False) or getattr(config.api_keys.bybit, 'testnet', False)
        if self.testnet:
            self.base_url = "https://api-testnet.bybit.com"
            self.ws_url = "wss://stream-testnet.bybit.com/v5/public/linear"
        else:
            self.base_url = "https://api.bybit.com"
            self.ws_url = "wss://stream.bybit.com/v5/public/linear"

        # API credentials - handle both config patterns
        self.api_key = getattr(config, 'bybit_api_key', '') or config.api_keys.bybit.get('api_key', '')
        self.api_secret = getattr(config, 'bybit_api_secret', '') or config.api_keys.bybit.get('api_secret', '')
        self.recv_window = getattr(config, 'bybit_recv_window', 5000)
        
        # HTTP session
        self.session = None
        
        # WebSocket connection
        self.ws = None
        self.ws_connected = False
        
        # Data storage
        self.market_data = {}
        self.account_data = {}
        self.positions = {}
        self.orders = {}
        
        # Rate limiting
        self.rate_limits = {
            'requests_per_second': 10,
            'last_request_time': 0.0,
            'request_count': 0
        }
        
    async def initialize(self):
        """Initialize the Bybit client"""
        try:
            logger.info("Initializing Bybit API client...")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                connector=aiohttp.TCPConnector(limit=100)
            )
            
            # Test API connection
            await self._test_connection()
            
            # Initialize WebSocket
            await self._initialize_websocket()
            
            logger.info("Bybit API client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Bybit client: {e}")
            raise
    
    async def close(self):
        """Close all connections"""
        try:
            if self.ws is not None and hasattr(self.ws, 'close'):
                close_method = getattr(self.ws, 'close')
                if asyncio.iscoroutinefunction(close_method):
                    await close_method()
                else:
                    # Handle synchronous close method
                    close_method()
            if self.session:
                await self.session.close()
            logger.info("Bybit client connections closed")
        except Exception as e:
            logger.error(f"Error closing Bybit client: {e}")
    
    async def _test_connection(self):
        """Test API connection"""
        try:
            response = await self._make_request("GET", "/v5/market/time")
            if response.get("retCode") == 0:
                server_time = response["result"]["timeSecond"]
                local_time = int(time.time())
                time_diff = abs(server_time - local_time)
                
                if time_diff > 5:  # 5 second tolerance
                    logger.warning(f"Time difference with server: {time_diff}s")
                
                logger.info("Bybit API connection test passed")
            else:
                raise Exception(f"API test failed: {response}")
                
        except Exception as e:
            logger.error(f"Bybit API connection test failed: {e}")
            raise
    
    async def _initialize_websocket(self):
        """Initialize WebSocket connection for real-time data"""
        try:
            # Note: Implement WebSocket connection here
            # For now, we'll use HTTP polling
            logger.info("WebSocket initialization (HTTP polling mode)")
            
        except Exception as e:
            logger.error(f"Failed to initialize WebSocket: {e}")
    
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, signed: bool = False) -> Dict:
        """Make HTTP request to Bybit API"""
        try:
            # Ensure session is initialized
            if self.session is None:
                raise RuntimeError("HTTP session not initialized. Call initialize() first.")

            # Rate limiting
            await self._rate_limit()

            url = f"{self.base_url}{endpoint}"
            headers = {
                "Content-Type": "application/json",
                "X-BAPI-API-KEY": self.api_key
            }

            if params is None:
                params = {}

            # Add timestamp for signed requests
            if signed:
                timestamp = str(int(time.time() * 1000))
                params["timestamp"] = timestamp
                params["recv_window"] = str(self.recv_window)

                # Create signature
                signature = self._create_signature(method, params, timestamp)
                headers["X-BAPI-SIGN"] = signature
                headers["X-BAPI-TIMESTAMP"] = timestamp
                headers["X-BAPI-RECV-WINDOW"] = str(self.recv_window)

            # Make request
            if method == "GET":
                if params:
                    url += f"?{urlencode(params)}"
                async with self.session.get(url, headers=headers) as response:
                    return await response.json()

            elif method == "POST":
                async with self.session.post(url, headers=headers, json=params) as response:
                    return await response.json()

            else:
                # Handle unsupported HTTP methods
                raise ValueError(f"Unsupported HTTP method: {method}")

        except Exception as e:
            logger.error(f"API request failed: {method} {endpoint} - {e}")
            raise
    
    def _create_signature(self, method: str, params: Dict, timestamp: str) -> str:
        """Create API signature for authenticated requests"""
        try:
            # Prepare the signing string
            if method == "GET":
                query_string = urlencode(sorted(params.items()))
                signing_string = f"{timestamp}{self.api_key}{self.recv_window}{query_string}"
            else:
                param_str = json.dumps(params, separators=(',', ':'))
                signing_string = f"{timestamp}{self.api_key}{self.recv_window}{param_str}"
            
            # Create signature
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                signing_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return signature
            
        except Exception as e:
            logger.error(f"Failed to create signature: {e}")
            raise
    
    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        
        # Reset counter every second
        if current_time - self.rate_limits['last_request_time'] >= 1:
            self.rate_limits['request_count'] = 0
            self.rate_limits['last_request_time'] = current_time
        
        # Check if we need to wait
        if self.rate_limits['request_count'] >= self.rate_limits['requests_per_second']:
            sleep_time = 1 - (current_time - self.rate_limits['last_request_time'])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                self.rate_limits['request_count'] = 0
                self.rate_limits['last_request_time'] = time.time()
        
        self.rate_limits['request_count'] += 1
    
    # Market Data Methods
    async def get_market_data(self, symbol: str, timeframe: str = "1", limit: int = 200) -> List[Dict]:
        """Get historical market data (OHLCV)"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": timeframe,
                "limit": limit
            }
            
            response = await self._make_request("GET", "/v5/market/kline", params)
            
            if response.get("retCode") == 0:
                klines = response["result"]["list"]
                
                # Convert to standard format
                market_data = []
                for kline in klines:
                    market_data.append({
                        "timestamp": datetime.fromtimestamp(int(kline[0]) / 1000),
                        "open": float(kline[1]),
                        "high": float(kline[2]),
                        "low": float(kline[3]),
                        "close": float(kline[4]),
                        "volume": float(kline[5])
                    })
                
                return market_data
            else:
                raise Exception(f"Failed to get market data: {response}")
                
        except Exception as e:
            logger.error(f"Failed to get market data for {symbol}: {e}")
            raise
    
    async def get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol
            }
            
            response = await self._make_request("GET", "/v5/market/tickers", params)
            
            if response.get("retCode") == 0:
                ticker = response["result"]["list"][0]
                return float(ticker["lastPrice"])
            else:
                raise Exception(f"Failed to get current price: {response}")
                
        except Exception as e:
            logger.error(f"Failed to get current price for {symbol}: {e}")
            raise
    
    async def get_order_book(self, symbol: str, limit: int = 25) -> Dict:
        """Get order book data"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol,
                "limit": limit
            }
            
            response = await self._make_request("GET", "/v5/market/orderbook", params)
            
            if response.get("retCode") == 0:
                orderbook = response["result"]
                return {
                    "bids": [[float(bid[0]), float(bid[1])] for bid in orderbook["b"]],
                    "asks": [[float(ask[0]), float(ask[1])] for ask in orderbook["a"]],
                    "timestamp": datetime.now(timezone.utc)
                }
            else:
                raise Exception(f"Failed to get order book: {response}")
                
        except Exception as e:
            logger.error(f"Failed to get order book for {symbol}: {e}")
            raise
    
    # Account Methods
    async def get_account_balance(self) -> Dict:
        """Get account balance"""
        try:
            params = {
                "accountType": "UNIFIED"
            }
            
            response = await self._make_request("GET", "/v5/account/wallet-balance", params, signed=True)
            
            if response.get("retCode") == 0:
                wallet = response["result"]["list"][0]
                
                balance_data = {
                    "total_equity": float(wallet["totalEquity"]),
                    "available_balance": float(wallet["totalAvailableBalance"]),
                    "unrealized_pnl": float(wallet["totalPerpUPL"]),
                    "used_margin": float(wallet["totalInitialMargin"]),
                    "coins": {}
                }
                
                for coin in wallet["coin"]:
                    balance_data["coins"][coin["coin"]] = {
                        "available": float(coin["availableToWithdraw"]),
                        "total": float(coin["walletBalance"]),
                        "unrealized_pnl": float(coin["unrealisedPnl"])
                    }
                
                return balance_data
            else:
                raise Exception(f"Failed to get account balance: {response}")
                
        except Exception as e:
            logger.error(f"Failed to get account balance: {e}")
            raise
    
    async def get_positions(self, symbol: Optional[str] = None) -> List[Dict]:
        """Get current positions"""
        try:
            params = {
                "category": "linear"
            }
            
            if symbol:
                params["symbol"] = symbol
            
            response = await self._make_request("GET", "/v5/position/list", params, signed=True)
            
            if response.get("retCode") == 0:
                positions = []
                for pos in response["result"]["list"]:
                    if float(pos["size"]) > 0:  # Only open positions
                        positions.append({
                            "symbol": pos["symbol"],
                            "side": pos["side"],
                            "size": float(pos["size"]),
                            "entry_price": float(pos["avgPrice"]),
                            "current_price": float(pos["markPrice"]),
                            "unrealized_pnl": float(pos["unrealisedPnl"]),
                            "percentage": float(pos["unrealisedPnl"]) / float(pos["positionValue"]) * 100 if float(pos["positionValue"]) > 0 else 0
                        })
                
                return positions
            else:
                raise Exception(f"Failed to get positions: {response}")
                
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            raise
    
    # Trading Methods
    async def place_order(self, 
                         symbol: str,
                         side: str,
                         quantity: float,
                         order_type: str = "Market",
                         price: Optional[float] = None,
                         stop_loss: Optional[float] = None,
                         take_profit: Optional[float] = None,
                         time_in_force: str = "GTC") -> Dict:
        """Place a trading order"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol,
                "side": side.capitalize(),
                "orderType": order_type,
                "qty": str(quantity),
                "timeInForce": time_in_force
            }
            
            if price and order_type != "Market":
                params["price"] = str(price)
            
            if stop_loss:
                params["stopLoss"] = str(stop_loss)
            
            if take_profit:
                params["takeProfit"] = str(take_profit)
            
            response = await self._make_request("POST", "/v5/order/create", params, signed=True)
            
            if response.get("retCode") == 0:
                order_data = {
                    "order_id": response["result"]["orderId"],
                    "symbol": symbol,
                    "side": side,
                    "quantity": quantity,
                    "order_type": order_type,
                    "price": price,
                    "status": "pending",
                    "timestamp": datetime.now(timezone.utc)
                }
                
                self.logger.log_trade(
                    action=f"ORDER_PLACED_{side.upper()}",
                    symbol=symbol,
                    quantity=quantity,
                    price=price or 0,
                    order_id=order_data["order_id"]
                )
                
                return order_data
            else:
                raise Exception(f"Failed to place order: {response}")
                
        except Exception as e:
            logger.error(f"Failed to place order: {e}")
            raise
    
    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an existing order"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol,
                "orderId": order_id
            }
            
            response = await self._make_request("POST", "/v5/order/cancel", params, signed=True)
            
            if response.get("retCode") == 0:
                logger.info(f"Order cancelled: {order_id}")
                return True
            else:
                logger.error(f"Failed to cancel order: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    async def get_order_status(self, symbol: str, order_id: str) -> Dict:
        """Get order status"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol,
                "orderId": order_id
            }
            
            response = await self._make_request("GET", "/v5/order/realtime", params, signed=True)
            
            if response.get("retCode") == 0:
                order = response["result"]["list"][0]
                return {
                    "order_id": order["orderId"],
                    "status": order["orderStatus"],
                    "symbol": order["symbol"],
                    "side": order["side"],
                    "quantity": float(order["qty"]),
                    "filled_quantity": float(order["cumExecQty"]),
                    "price": float(order["price"]) if order["price"] else None,
                    "avg_price": float(order["avgPrice"]) if order["avgPrice"] else None
                }
            else:
                raise Exception(f"Failed to get order status: {response}")
                
        except Exception as e:
            logger.error(f"Failed to get order status for {order_id}: {e}")
            raise
    
    async def close_position(self, symbol: str) -> bool:
        """Close entire position for a symbol"""
        try:
            # Get current position
            positions = await self.get_positions(symbol)
            
            if not positions:
                logger.info(f"No open position for {symbol}")
                return True
            
            position = positions[0]
            
            # Place opposite order to close position
            opposite_side = "Sell" if position["side"] == "Buy" else "Buy"
            
            order_result = await self.place_order(
                symbol=symbol,
                side=opposite_side,
                quantity=position["size"],
                order_type="Market"
            )
            
            if order_result:
                logger.info(f"Position closed for {symbol}")
                return True
            else:
                logger.error(f"Failed to close position for {symbol}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to close position for {symbol}: {e}")
            return False
    
    # Utility Methods
    async def get_trading_symbols(self) -> List[str]:
        """Get list of available trading symbols"""
        try:
            params = {
                "category": "linear"
            }
            
            response = await self._make_request("GET", "/v5/market/instruments-info", params)
            
            if response.get("retCode") == 0:
                symbols = []
                for instrument in response["result"]["list"]:
                    if instrument["status"] == "Trading":
                        symbols.append(instrument["symbol"])
                
                return symbols
            else:
                raise Exception(f"Failed to get trading symbols: {response}")
                
        except Exception as e:
            logger.error(f"Failed to get trading symbols: {e}")
            raise
    
    async def cancel_all_orders(self, symbol: Optional[str] = None) -> bool:
        """Cancel all orders for a symbol or all symbols"""
        try:
            params = {"category": "linear"}

            if symbol:
                params["symbol"] = symbol

            response = await self._make_request("POST", "/v5/order/cancel-all", params, signed=True)

            if response.get("retCode") == 0:
                logger.info(f"All orders cancelled for {symbol or 'all symbols'}")
                return True
            else:
                logger.error(f"Failed to cancel orders: {response}")
                return False

        except Exception as e:
            logger.error(f"Error cancelling orders: {e}")
            return False

    async def get_position(self, symbol: str) -> Optional[Dict]:
        """Get position for a specific symbol"""
        try:
            positions = await self.get_positions(symbol)
            if positions:
                return positions[0]  # Return first position for the symbol
            return None
        except Exception as e:
            logger.error(f"Error getting position for {symbol}: {e}")
            return None

    async def is_market_open(self, symbol: str = "BTCUSDT") -> bool:
        """Check if market is open for trading"""
        try:
            current_price = await self.get_current_price(symbol)
            return current_price > 0
        except Exception:
            return False
