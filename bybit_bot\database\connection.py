"""
Database Connection and Management for Bybit Trading Bot
Handles PostgreSQL connections, session management, and data persistence
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import NullPool

from bybit_bot.core.config import BotConfig
from bybit_bot.database.models import Base, Trade, Performance, SystemLog, Position

logger = logging.getLogger("bybit_trading_bot.database")


class DatabaseManager:
    """
    Manages database connections and operations for the trading bot
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.async_engine = None
        self.sync_engine = None
        self.async_session_factory = None
        self.sync_session_factory = None
        self._initialized = False
        
    async def initialize(self):
        """Initialize database connections and create tables"""
        try:
            logger.info("🔌 Initializing database connections...")

            # Get database URL from config
            db_url = getattr(self.config, 'database_url', None)
            if not db_url and hasattr(self.config, 'database'):
                db_url = getattr(self.config.database, 'url', None)

            if not db_url:
                db_url = "postgresql://postgres:password@localhost:5432/bybit_trading_bot"
                logger.warning("⚠️ Using default database URL")

            # Convert to async URL
            if db_url.startswith('postgresql://'):
                async_db_url = db_url.replace('postgresql://', 'postgresql+asyncpg://')
            elif db_url.startswith('sqlite://'):
                async_db_url = db_url.replace('sqlite://', 'sqlite+aiosqlite://')
            else:
                async_db_url = db_url

            # Create async engine
            self.async_engine = create_async_engine(
                async_db_url,
                poolclass=NullPool,
                echo=getattr(self.config, 'debug_mode', False),
                future=True
            )

            # Create sync engine for certain operations
            self.sync_engine = create_engine(
                db_url,
                pool_pre_ping=True,
                echo=getattr(self.config, 'debug_mode', False)
            )
            
            # Create session factories
            self.async_session_factory = async_sessionmaker(
                self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            self.sync_session_factory = sessionmaker(
                bind=self.sync_engine,
                expire_on_commit=False
            )
            
            # Test connection
            await self._test_connection()
            
            # Create tables
            await self._create_tables()
            
            self._initialized = True
            logger.info("✅ Database initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize database: {e}")
            raise
    
    async def _test_connection(self):
        """Test database connection"""
        try:
            if self.async_engine is None:
                raise RuntimeError("Async engine not initialized")
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                row = result.fetchone()
                if row and row[0] != 1:
                    raise Exception("Database connection test failed")
            logger.info("✅ Database connection test passed")
        except Exception as e:
            logger.error(f"❌ Database connection test failed: {e}")
            raise
    
    async def _create_tables(self):
        """Create database tables if they don't exist"""
        try:
            if self.async_engine is None:
                raise RuntimeError("Async engine not initialized")
            async with self.async_engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("✅ Database tables created/verified")
        except Exception as e:
            logger.error(f"❌ Failed to create tables: {e}")
            raise
    
    @asynccontextmanager
    async def get_async_session(self):
        """Get async database session with automatic cleanup"""
        if not self._initialized:
            raise RuntimeError("Database manager not initialized")

        if self.async_session_factory is None:
            raise RuntimeError("Async session factory not initialized")

        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error(f"Database session error: {e}")
                raise
            finally:
                await session.close()
    
    def get_sync_session(self):
        """Get sync database session"""
        if not self._initialized:
            raise RuntimeError("Database manager not initialized")

        if self.sync_session_factory is None:
            raise RuntimeError("Sync session factory not initialized")

        return self.sync_session_factory()
    
    async def close(self):
        """Close database connections"""
        if self.async_engine:
            await self.async_engine.dispose()
        if self.sync_engine:
            self.sync_engine.dispose()
        logger.info("🔌 Database connections closed")
    
    # Trading Data Methods
    async def save_trade(self, trade_data: Dict[str, Any]) -> int:
        """Save trade data to database"""
        try:
            async with self.get_async_session() as session:
                trade = Trade(
                    symbol=trade_data["symbol"],
                    side=trade_data["side"],
                    quantity=trade_data["quantity"],
                    price=trade_data["price"],
                    order_id=trade_data.get("order_id"),
                    exchange_order_id=trade_data.get("exchange_order_id"),
                    status=trade_data.get("status", "completed"),
                    strategy=trade_data.get("strategy"),
                    entry_price=trade_data.get("entry_price"),
                    exit_price=trade_data.get("exit_price"),
                    profit_loss=trade_data.get("profit_loss", 0.0),
                    fees=trade_data.get("fees", 0.0),
                    executed_at=trade_data.get("executed_at", datetime.now(timezone.utc)),
                    extra_data=trade_data.get("extra_data", {})
                )
                
                session.add(trade)
                await session.flush()
                await session.refresh(trade)
                return trade.id  # type: ignore
                
        except Exception as e:
            logger.error(f"Failed to save trade: {e}")
            raise
    
    async def get_trades(self, 
                        symbol: Optional[str] = None,
                        strategy: Optional[str] = None,
                        start_date: Optional[datetime] = None,
                        end_date: Optional[datetime] = None,
                        limit: int = 100) -> List[Trade]:
        """Get trades from database with optional filters"""
        try:
            async with self.get_async_session() as session:
                from sqlalchemy import select
                
                query = select(Trade)
                
                if symbol:
                    query = query.where(Trade.symbol == symbol)
                if strategy:
                    query = query.where(Trade.strategy == strategy)
                if start_date:
                    query = query.where(Trade.executed_at >= start_date)
                if end_date:
                    query = query.where(Trade.executed_at <= end_date)
                
                query = query.order_by(Trade.executed_at.desc()).limit(limit)
                result = await session.execute(query)
                return list(result.scalars().all())
                
        except Exception as e:
            logger.error(f"Failed to get trades: {e}")
            raise
    
    async def save_position(self, position_data: Dict[str, Any]) -> int:
        """Save position data to database"""
        try:
            async with self.get_async_session() as session:
                position = Position(
                    symbol=position_data["symbol"],
                    side=position_data["side"],
                    size=position_data["size"],
                    entry_price=position_data["entry_price"],
                    current_price=position_data.get("current_price"),
                    unrealized_pnl=position_data.get("unrealized_pnl", 0.0),
                    strategy=position_data.get("strategy"),
                    stop_loss=position_data.get("stop_loss"),
                    take_profit=position_data.get("take_profit"),
                    opened_at=position_data.get("opened_at", datetime.now(timezone.utc)),
                    status=position_data.get("status", "open"),
                    extra_data=position_data.get("extra_data", {})
                )
                
                session.add(position)
                await session.flush()
                await session.refresh(position)
                return position.id  # type: ignore
                
        except Exception as e:
            logger.error(f"Failed to save position: {e}")
            raise
    
    async def update_position(self, position_id: int, update_data: Dict[str, Any]):
        """Update position data"""
        try:
            async with self.get_async_session() as session:
                position = await session.get(Position, position_id)
                if position:
                    for key, value in update_data.items():
                        if key != 'updated_at':  # Don't allow manual updated_at override
                            setattr(position, key, value)
                    # updated_at will be handled by SQLAlchemy onupdate
                
        except Exception as e:
            logger.error(f"Failed to update position: {e}")
            raise
    
    async def get_open_positions(self, symbol: Optional[str] = None) -> List[Position]:
        """Get open positions"""
        try:
            async with self.get_async_session() as session:
                from sqlalchemy import select
                
                query = select(Position).where(Position.status == "open")
                
                if symbol:
                    query = query.where(Position.symbol == symbol)
                
                result = await session.execute(query)
                return list(result.scalars().all())
                
        except Exception as e:
            logger.error(f"Failed to get open positions: {e}")
            raise
    
    async def save_performance_data(self, performance_data: Dict[str, Any]) -> Optional[int]:
        """Save performance metrics to database"""
        try:
            async with self.get_async_session() as session:
                performance = Performance(
                    strategy=performance_data.get("strategy"),
                    symbol=performance_data.get("symbol"),
                    period_start=performance_data["period_start"],
                    period_end=performance_data["period_end"],
                    total_trades=performance_data.get("total_trades", 0),
                    winning_trades=performance_data.get("winning_trades", 0),
                    losing_trades=performance_data.get("losing_trades", 0),
                    win_rate=performance_data.get("win_rate", 0.0),
                    total_pnl=performance_data.get("total_pnl", 0.0),
                    avg_win=performance_data.get("avg_win", 0.0),
                    avg_loss=performance_data.get("avg_loss", 0.0),
                    max_drawdown=performance_data.get("max_drawdown", 0.0),
                    sharpe_ratio=performance_data.get("sharpe_ratio", 0.0),
                    volatility=performance_data.get("volatility", 0.0),
                    extra_data=performance_data.get("extra_data", {})
                )
                
                session.add(performance)
                await session.flush()
                await session.refresh(performance)
                return performance.id  # type: ignore
                
        except Exception as e:
            logger.error(f"Failed to save performance data: {e}")
            raise
    
    async def get_performance_data(self,
                                  strategy: Optional[str] = None,
                                  symbol: Optional[str] = None,
                                  days: int = 30) -> List[Performance]:
        """Get performance data"""
        try:
            async with self.get_async_session() as session:
                from sqlalchemy import select
                
                start_date = datetime.now(timezone.utc) - timedelta(days=days)
                
                query = select(Performance).where(
                    Performance.period_start >= start_date
                )
                
                if strategy:
                    query = query.where(Performance.strategy == strategy)
                if symbol:
                    query = query.where(Performance.symbol == symbol)
                
                query = query.order_by(Performance.period_start.desc())
                result = await session.execute(query)
                return list(result.scalars().all())
                
        except Exception as e:
            logger.error(f"Failed to get performance data: {e}")
            raise
    
    async def save_system_log(self, log_data: Dict[str, Any]) -> int:
        """Save system log entry"""
        try:
            async with self.get_async_session() as session:
                log_entry = SystemLog(
                    level=log_data["level"],
                    component=log_data["component"],
                    message=log_data["message"],
                    details=log_data.get("details", {}),
                    created_at=log_data.get("created_at", datetime.now(timezone.utc))
                )
                
                session.add(log_entry)
                await session.flush()
                await session.refresh(log_entry)
                return log_entry.id  # type: ignore
                
        except Exception as e:
            logger.error(f"Failed to save system log: {e}")
            raise
    
    async def cleanup_old_data(self, days_to_keep: int = 30):
        """Clean up old data to prevent database bloat"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            
            async with self.get_async_session() as session:
                # Clean up old system logs
                await session.execute(
                    text("DELETE FROM system_logs WHERE created_at < :cutoff"),
                    {"cutoff": cutoff_date}
                )
                
                # Clean up old performance data (keep longer)
                performance_cutoff = datetime.now(timezone.utc) - timedelta(days=days_to_keep * 3)
                await session.execute(
                    text("DELETE FROM performance WHERE period_start < :cutoff"),
                    {"cutoff": performance_cutoff}
                )
                
            logger.info(f"✅ Cleaned up data older than {days_to_keep} days")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
            raise
    
    async def execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None):
        """Execute raw SQL statement"""
        try:
            async with self.get_async_session() as session:
                if params:
                    result = await session.execute(text(sql), params)
                else:
                    result = await session.execute(text(sql))
                
                await session.commit()
                return result
                
        except Exception as e:
            logger.error(f"Failed to execute SQL: {e}")
            raise
    
    async def execute_sql_with_result(self, sql: str, params: Optional[Dict] = None) -> List[Dict]:
        """Execute SQL and return results as list of dictionaries"""
        try:
            async with self.get_async_session() as session:
                result = await session.execute(text(sql), params or {})
                columns = result.keys()
                rows = result.fetchall()
                return [dict(zip(columns, row)) for row in rows]

        except Exception as e:
            logger.error(f"Failed to execute SQL with result: {e}")
            raise

    async def fetch_all(self, sql: str, *params) -> List[Dict]:
        """Fetch all results from SQL query"""
        try:
            async with self.get_async_session() as session:
                if params:
                    # Convert positional parameters to named parameters
                    param_dict = {f'param_{i}': param for i, param in enumerate(params)}
                    # Replace $1, $2, etc. with :param_0, :param_1, etc.
                    formatted_sql = sql
                    for i, _ in enumerate(params):
                        formatted_sql = formatted_sql.replace(f'${i+1}', f':param_{i}')
                    result = await session.execute(text(formatted_sql), param_dict)
                else:
                    result = await session.execute(text(sql))

                columns = result.keys()
                rows = result.fetchall()
                return [dict(zip(columns, row)) for row in rows]

        except Exception as e:
            logger.error(f"Failed to fetch all results: {e}")
            raise

    async def fetch_one(self, sql: str, *params) -> Optional[Dict]:
        """Fetch one result from SQL query"""
        try:
            async with self.get_async_session() as session:
                if params:
                    # Convert positional parameters to named parameters
                    param_dict = {f'param_{i}': param for i, param in enumerate(params)}
                    # Replace $1, $2, etc. with :param_0, :param_1, etc.
                    formatted_sql = sql
                    for i, _ in enumerate(params):
                        formatted_sql = formatted_sql.replace(f'${i+1}', f':param_{i}')
                    result = await session.execute(text(formatted_sql), param_dict)
                else:
                    result = await session.execute(text(sql))

                row = result.fetchone()
                if row:
                    columns = result.keys()
                    return dict(zip(columns, row))
                return None

        except Exception as e:
            logger.error(f"Failed to fetch one result: {e}")
            raise
