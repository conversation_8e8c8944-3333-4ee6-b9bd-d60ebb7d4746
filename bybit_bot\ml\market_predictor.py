"""
ML Market Predictor
Advanced machine learning system for price prediction and market analysis
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pickle
import joblib
from pathlib import Path

# ML Libraries
try:
    import tensorflow as tf
    from tensorflow import keras
    TENSORFLOW_AVAILABLE = True
except ImportError:
    tf = None
    keras = None
    TENSORFLOW_AVAILABLE = False
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from scipy import stats

# Technical Analysis
import talib
import ta

from ..core.config import BotConfig
from ..database.connection import DatabaseManager


class MLMarketPredictor:
    """
    Advanced ML predictor that:
    - Uses multiple ML models for ensemble predictions
    - Incorporates technical indicators, sentiment, and economic data
    - Provides short-term and long-term predictions
    - Continuously learns and adapts
    - Calculates prediction confidence
    """
    
    def __init__(self, config: BotConfig, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.running = False
        
        # Model storage path
        self.models_path = Path("models")
        self.models_path.mkdir(exist_ok=True)
        
        # ML Models
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        
        # Prediction horizons (in minutes)
        self.prediction_horizons = {
            'short_term': 15,    # 15 minutes
            'medium_term': 60,   # 1 hour
            'long_term': 240,    # 4 hours
            'daily': 1440        # 24 hours
        }
        
        # Model types
        self.model_types = [
            'lstm_deep',
            'xgboost',
            'lightgbm',
            'random_forest',
            'gradient_boosting'
        ]
        
        self.prediction_tasks = []
        
    async def start(self):
        """Start the ML prediction system"""
        if self.running:
            return
            
        self.running = True
        self.logger.info("🤖 Starting ML market predictor...")
        
        # Initialize models
        await self._initialize_models()
        
        # Start prediction tasks
        self.prediction_tasks = [
            asyncio.create_task(self._continuous_training()),
            asyncio.create_task(self._generate_predictions()),
            asyncio.create_task(self._evaluate_model_performance()),
            asyncio.create_task(self._optimize_models()),
            asyncio.create_task(self._calculate_market_signals()),
        ]
        
        await asyncio.gather(*self.prediction_tasks, return_exceptions=True)
    
    async def stop(self):
        """Stop the ML prediction system"""
        self.running = False
        
        # Cancel all tasks
        for task in self.prediction_tasks:
            task.cancel()
            
        self.logger.info("🛑 ML market predictor stopped")
    
    async def _initialize_models(self):
        """Initialize all ML models"""
        try:
            # Load existing models or create new ones
            for symbol in self.config.get_trading_pairs():
                self.models[symbol] = {}
                self.scalers[symbol] = {}
                
                for model_type in self.model_types:
                    # Skip LSTM models if TensorFlow is not available
                    if model_type == 'lstm_deep' and not TENSORFLOW_AVAILABLE:
                        self.logger.warning(f"Skipping {model_type} model for {symbol} - TensorFlow not available")
                        continue

                    model_path = self.models_path / f"{symbol}_{model_type}.pkl"
                    scaler_path = self.models_path / f"{symbol}_{model_type}_scaler.pkl"

                    if model_path.exists() and scaler_path.exists():
                        # Load existing model
                        self.models[symbol][model_type] = joblib.load(model_path)
                        self.scalers[symbol][model_type] = joblib.load(scaler_path)
                        self.logger.info(f"Loaded existing {model_type} model for {symbol}")
                    else:
                        # Create new model
                        model = self._create_model(model_type)
                        if model is not None:  # Only add if model creation succeeded
                            self.models[symbol][model_type] = model
                            self.scalers[symbol][model_type] = StandardScaler()
                            self.logger.info(f"Created new {model_type} model for {symbol}")
            
            self.logger.info("ML models initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing models: {e}")
    
    def _create_model(self, model_type: str):
        """Create a new ML model of specified type"""
        if model_type == 'lstm_deep':
            if not TENSORFLOW_AVAILABLE:
                self.logger.warning("TensorFlow not available, skipping LSTM model creation")
                return None
            return self._create_lstm_model()
        elif model_type == 'xgboost':
            return xgb.XGBRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
        elif model_type == 'lightgbm':
            return lgb.LGBMRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                verbosity=-1
            )
        elif model_type == 'random_forest':
            return RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'gradient_boosting':
            return GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
    
    def _create_lstm_model(self):
        """Create LSTM deep learning model"""
        if not TENSORFLOW_AVAILABLE or keras is None:
            raise ImportError("TensorFlow is not available. Cannot create LSTM model.")

        model = keras.Sequential([
            keras.layers.LSTM(50, return_sequences=True, input_shape=(60, 1)),
            keras.layers.Dropout(0.2),
            keras.layers.LSTM(50, return_sequences=True),
            keras.layers.Dropout(0.2),
            keras.layers.LSTM(50),
            keras.layers.Dropout(0.2),
            keras.layers.Dense(25),
            keras.layers.Dense(1)
        ])
        
        model.compile(
            optimizer='adam',
            loss='mean_squared_error',
            metrics=['mae']
        )
        
        return model
    
    async def _continuous_training(self):
        """Continuously train models with new data"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    # Get training data
                    training_data = await self._prepare_training_data(symbol)
                    
                    if training_data is not None and len(training_data) > 100:
                        # Train each model type
                        for model_type in self.model_types:
                            await self._train_model(symbol, model_type, training_data)
                        
                        self.logger.info(f"Updated models for {symbol}")
                    
                    await asyncio.sleep(60)  # Wait between symbols
                
                await asyncio.sleep(self.config.ml_prediction.training_interval)
                
            except Exception as e:
                self.logger.error(f"Error in continuous training: {e}")
                await asyncio.sleep(1800)
    
    async def _generate_predictions(self):
        """Generate predictions for all symbols and horizons"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    # Get current market data for features
                    features = await self._prepare_prediction_features(symbol)
                    
                    if features is not None:
                        predictions = {}
                        
                        # Generate predictions for each horizon
                        for horizon_name, horizon_minutes in self.prediction_horizons.items():
                            ensemble_prediction = await self._ensemble_predict(
                                symbol, features, horizon_minutes
                            )
                            
                            if ensemble_prediction is not None:
                                predictions[horizon_name] = ensemble_prediction
                        
                        if predictions:
                            await self._store_predictions(symbol, predictions)
                
                await asyncio.sleep(self.config.ml_prediction.prediction_interval)
                
            except Exception as e:
                self.logger.error(f"Error generating predictions: {e}")
                await asyncio.sleep(900)
    
    async def _evaluate_model_performance(self):
        """Evaluate and track model performance"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    # Evaluate predictions vs actual prices
                    performance_metrics = await self._calculate_prediction_accuracy(symbol)
                    
                    if performance_metrics:
                        await self._store_model_performance(symbol, performance_metrics)
                        
                        # Check if models need retraining
                        if performance_metrics.get('ensemble_accuracy', 0) < 0.6:
                            self.logger.warning(f"Low accuracy for {symbol}, scheduling retraining")
                
                await asyncio.sleep(3600)  # Evaluate every hour
                
            except Exception as e:
                self.logger.error(f"Error evaluating model performance: {e}")
                await asyncio.sleep(1800)
    
    async def _optimize_models(self):
        """Optimize model hyperparameters"""
        while self.running:
            try:
                # Periodic model optimization
                for symbol in self.config.get_trading_pairs():
                    await self._hyperparameter_optimization(symbol)
                    await asyncio.sleep(300)  # Wait between symbols
                
                await asyncio.sleep(86400)  # Optimize daily
                
            except Exception as e:
                self.logger.error(f"Error optimizing models: {e}")
                await asyncio.sleep(7200)
    
    async def _calculate_market_signals(self):
        """Calculate trading signals based on ML predictions"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    signals = await self._generate_trading_signals(symbol)
                    
                    if signals:
                        await self._store_trading_signals(symbol, signals)
                
                await asyncio.sleep(self.config.ml_prediction.signal_interval)
                
            except Exception as e:
                self.logger.error(f"Error calculating market signals: {e}")
                await asyncio.sleep(600)
    
    async def _prepare_training_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Prepare comprehensive training data"""
        try:
            # Get historical price data (last 30 days)
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            price_query = """
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM market_data 
            WHERE symbol = $1 AND timestamp > $2 
            ORDER BY timestamp ASC
            """
            
            price_data = await self.db_manager.fetch_all(price_query, symbol, cutoff_date)
            
            if len(price_data) < 100:
                return None
            
            df = pd.DataFrame(price_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # Add technical indicators
            df = self._add_technical_indicators(df)
            
            # Add sentiment data
            df = await self._add_sentiment_features(df, symbol)
            
            # Add economic data
            df = await self._add_economic_features(df)
            
            # Add market microstructure features
            df = await self._add_microstructure_features(df, symbol)
            
            # Create target variables for different horizons
            for horizon_name, horizon_minutes in self.prediction_horizons.items():
                df[f'target_{horizon_name}'] = df['close_price'].shift(-horizon_minutes)
            
            # Remove rows with NaN values
            df.dropna(inplace=True)
            
            if len(df) < 50:
                return None
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error preparing training data for {symbol}: {e}")
            return None
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical analysis indicators"""
        try:
            # Price-based indicators
            df['sma_5'] = talib.SMA(df['close_price'], timeperiod=5)
            df['sma_20'] = talib.SMA(df['close_price'], timeperiod=20)
            df['sma_50'] = talib.SMA(df['close_price'], timeperiod=50)
            df['ema_12'] = talib.EMA(df['close_price'], timeperiod=12)
            df['ema_26'] = talib.EMA(df['close_price'], timeperiod=26)
            
            # MACD
            df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close_price'])
            
            # RSI
            df['rsi'] = talib.RSI(df['close_price'], timeperiod=14)
            
            # Bollinger Bands
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close_price'])
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            df['bb_position'] = (df['close_price'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Stochastic
            df['stoch_k'], df['stoch_d'] = talib.STOCH(df['high_price'], df['low_price'], df['close_price'])
            
            # Williams %R
            df['williams_r'] = talib.WILLR(df['high_price'], df['low_price'], df['close_price'])
            
            # ADX
            df['adx'] = talib.ADX(df['high_price'], df['low_price'], df['close_price'])
            
            # CCI
            df['cci'] = talib.CCI(df['high_price'], df['low_price'], df['close_price'])
            
            # Volume indicators
            df['volume_sma'] = talib.SMA(df['volume'], timeperiod=20)
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            df['ad_line'] = talib.AD(df['high_price'], df['low_price'], df['close_price'], df['volume'])
            df['obv'] = talib.OBV(df['close_price'], df['volume'])
            
            # Price patterns
            df['doji'] = talib.CDLDOJI(df['open_price'], df['high_price'], df['low_price'], df['close_price'])
            df['hammer'] = talib.CDLHAMMER(df['open_price'], df['high_price'], df['low_price'], df['close_price'])
            df['engulfing'] = talib.CDLENGULFING(df['open_price'], df['high_price'], df['low_price'], df['close_price'])
            
            # Volatility
            df['atr'] = talib.ATR(df['high_price'], df['low_price'], df['close_price'])
            df['volatility'] = df['close_price'].rolling(20).std()
            
            # Price momentum
            df['momentum'] = talib.MOM(df['close_price'], timeperiod=10)
            df['roc'] = talib.ROC(df['close_price'], timeperiod=10)
            
            # Support/Resistance levels
            df['pivot'] = (df['high_price'] + df['low_price'] + df['close_price']) / 3
            df['resistance1'] = 2 * df['pivot'] - df['low_price']
            df['support1'] = 2 * df['pivot'] - df['high_price']
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error adding technical indicators: {e}")
            return df
    
    async def _add_sentiment_features(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Add sentiment analysis features"""
        try:
            # Get sentiment data
            sentiment_query = """
            SELECT 
                DATE_TRUNC('hour', timestamp) as hour,
                AVG(sentiment_compound) as avg_sentiment,
                COUNT(*) as sentiment_volume
            FROM news_sentiment 
            WHERE timestamp > $1 AND (title ILIKE $2 OR content ILIKE $2)
            GROUP BY hour
            ORDER BY hour
            """
            
            symbol_clean = symbol.replace('USDT', '')
            cutoff_date = df.index.min()
            
            sentiment_data = await self.db_manager.fetch_all(
                sentiment_query, cutoff_date, f"%{symbol_clean}%"
            )
            
            if sentiment_data:
                sentiment_df = pd.DataFrame(sentiment_data)
                sentiment_df['hour'] = pd.to_datetime(sentiment_df['hour'])
                sentiment_df.set_index('hour', inplace=True)
                
                # Resample to match price data frequency
                sentiment_resampled = sentiment_df.resample('1min').ffill()
                
                # Merge with price data
                df = df.join(sentiment_resampled, how='left')
                df['avg_sentiment'].fillna(0, inplace=True)
                df['sentiment_volume'].fillna(0, inplace=True)
            else:
                df['avg_sentiment'] = 0
                df['sentiment_volume'] = 0
            
            # Get social sentiment
            social_query = """
            SELECT 
                DATE_TRUNC('hour', created_at) as hour,
                AVG(sentiment_compound) as social_sentiment,
                SUM(engagement_score) as social_engagement
            FROM social_sentiment 
            WHERE created_at > $1 AND (keyword ILIKE $2 OR crypto_mentions ILIKE $2)
            GROUP BY hour
            ORDER BY hour
            """
            
            social_data = await self.db_manager.fetch_all(
                social_query, cutoff_date, f"%{symbol_clean}%"
            )
            
            if social_data:
                social_df = pd.DataFrame(social_data)
                social_df['hour'] = pd.to_datetime(social_df['hour'])
                social_df.set_index('hour', inplace=True)
                
                social_resampled = social_df.resample('1min').ffill()
                df = df.join(social_resampled, how='left')
                df['social_sentiment'].fillna(0, inplace=True)
                df['social_engagement'].fillna(0, inplace=True)
            else:
                df['social_sentiment'] = 0
                df['social_engagement'] = 0
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error adding sentiment features: {e}")
            return df
    
    async def _add_economic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add economic indicators as features"""
        try:
            # Get economic indexes
            econ_query = """
            SELECT timestamp, fear_greed_index, inflation_index, liquidity_index, momentum_index
            FROM economic_indexes 
            WHERE timestamp > $1
            ORDER BY timestamp
            """
            
            cutoff_date = df.index.min()
            econ_data = await self.db_manager.fetch_all(econ_query, cutoff_date)
            
            if econ_data:
                econ_df = pd.DataFrame(econ_data)
                econ_df['timestamp'] = pd.to_datetime(econ_df['timestamp'])
                econ_df.set_index('timestamp', inplace=True)
                
                # Resample and forward fill
                econ_resampled = econ_df.resample('1min').ffill()
                
                # Merge with price data
                df = df.join(econ_resampled, how='left')
                
                # Fill NaN values with neutral values
                for col in ['fear_greed_index', 'inflation_index', 'liquidity_index', 'momentum_index']:
                    df[col].fillna(50, inplace=True)
            else:
                # Add neutral economic features
                df['fear_greed_index'] = 50
                df['inflation_index'] = 50
                df['liquidity_index'] = 50
                df['momentum_index'] = 50
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error adding economic features: {e}")
            return df
    
    async def _add_microstructure_features(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Add market microstructure features"""
        try:
            # Calculate bid-ask spread features
            spread_query = """
            SELECT 
                timestamp,
                AVG(best_ask - best_bid) as avg_spread,
                AVG((best_ask - best_bid) / ((best_ask + best_bid) / 2)) as relative_spread
            FROM order_book_snapshots 
            WHERE symbol = $1 AND timestamp > $2
            GROUP BY timestamp
            ORDER BY timestamp
            """
            
            cutoff_date = df.index.min()
            spread_data = await self.db_manager.fetch_all(spread_query, symbol, cutoff_date)
            
            if spread_data:
                spread_df = pd.DataFrame(spread_data)
                spread_df['timestamp'] = pd.to_datetime(spread_df['timestamp'])
                spread_df.set_index('timestamp', inplace=True)
                
                spread_resampled = spread_df.resample('1min').mean()
                df = df.join(spread_resampled, how='left')
                
                df['avg_spread'].fillna(df['avg_spread'].mean(), inplace=True)
                df['relative_spread'].fillna(df['relative_spread'].mean(), inplace=True)
            else:
                df['avg_spread'] = 0
                df['relative_spread'] = 0
            
            # Add order flow features
            df['price_change'] = df['close_price'].pct_change()
            df['log_return'] = np.log(df['close_price'] / df['close_price'].shift(1))
            df['high_low_ratio'] = df['high_price'] / df['low_price']
            df['price_range'] = (df['high_price'] - df['low_price']) / df['close_price']
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error adding microstructure features: {e}")
            return df
    
    async def _prepare_prediction_features(self, symbol: str) -> Optional[np.ndarray]:
        """Prepare features for current prediction"""
        try:
            # Get recent data for feature calculation
            cutoff_date = datetime.utcnow() - timedelta(hours=4)
            
            price_query = """
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM market_data 
            WHERE symbol = $1 AND timestamp > $2 
            ORDER BY timestamp DESC
            LIMIT 60
            """
            
            price_data = await self.db_manager.fetch_all(price_query, symbol, cutoff_date)
            
            if len(price_data) < 30:
                return None
            
            df = pd.DataFrame(price_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()  # Ensure chronological order
            
            # Add all features (same as training)
            df = self._add_technical_indicators(df)
            df = await self._add_sentiment_features(df, symbol)
            df = await self._add_economic_features(df)
            df = await self._add_microstructure_features(df, symbol)
            
            # Remove NaN values
            df.dropna(inplace=True)
            
            if len(df) == 0:
                return None
            
            # Get feature columns (exclude targets and timestamp)
            feature_cols = [col for col in df.columns if not col.startswith('target_')]
            
            # Store feature columns for consistency
            if not self.feature_columns:
                self.feature_columns = feature_cols
            
            # Return the latest features
            return df[feature_cols].iloc[-1:].values
            
        except Exception as e:
            self.logger.error(f"Error preparing prediction features for {symbol}: {e}")
            return None
    
    async def _train_model(self, symbol: str, model_type: str, training_data: pd.DataFrame):
        """Train a specific model"""
        try:
            if model_type not in self.models[symbol]:
                return
            
            # Prepare features and targets
            feature_cols = [col for col in training_data.columns if not col.startswith('target_')]
            X = training_data[feature_cols].values
            
            # Scale features
            X_scaled = self.scalers[symbol][model_type].fit_transform(X)
            
            # Train for each prediction horizon
            model = self.models[symbol][model_type]
            
            # Use medium-term target for training
            y = training_data['target_medium_term'].values
            
            # Remove NaN values
            valid_idx = ~np.isnan(y)
            X_scaled = X_scaled[valid_idx]
            y = y[valid_idx]
            
            if len(X_scaled) < 20:
                return
            
            # Train model based on type
            if model_type == 'lstm_deep':
                if not TENSORFLOW_AVAILABLE:
                    self.logger.warning(f"TensorFlow not available, skipping LSTM training for {symbol}")
                    return

                # Reshape for LSTM (samples, timesteps, features)
                X_lstm = X_scaled.reshape((X_scaled.shape[0], 1, X_scaled.shape[1]))

                model.fit(
                    X_lstm, y,
                    epochs=50,
                    batch_size=32,
                    validation_split=0.2,
                    verbose=0
                )
            else:
                # Train sklearn-based models
                model.fit(X_scaled, y)
            
            # Save trained model
            model_path = self.models_path / f"{symbol}_{model_type}.pkl"
            scaler_path = self.models_path / f"{symbol}_{model_type}_scaler.pkl"
            
            joblib.dump(model, model_path)
            joblib.dump(self.scalers[symbol][model_type], scaler_path)
            
            self.logger.debug(f"Trained {model_type} model for {symbol}")
            
        except Exception as e:
            self.logger.error(f"Error training {model_type} for {symbol}: {e}")
    
    async def _ensemble_predict(self, symbol: str, features: np.ndarray, horizon_minutes: int) -> Optional[Dict]:
        """Generate ensemble prediction from multiple models"""
        try:
            predictions = {}
            confidences = {}
            
            for model_type in self.model_types:
                if model_type not in self.models[symbol]:
                    continue
                
                model = self.models[symbol][model_type]
                scaler = self.scalers[symbol][model_type]
                
                # Scale features
                features_scaled = scaler.transform(features)
                
                # Make prediction based on model type
                if model_type == 'lstm_deep':
                    if not TENSORFLOW_AVAILABLE:
                        self.logger.warning(f"TensorFlow not available, skipping LSTM prediction for {symbol}")
                        continue

                    # Reshape for LSTM
                    features_lstm = features_scaled.reshape((features_scaled.shape[0], 1, features_scaled.shape[1]))
                    pred = model.predict(features_lstm, verbose=0)[0][0]
                else:
                    pred = model.predict(features_scaled)[0]
                
                predictions[model_type] = float(pred)
                
                # Calculate confidence (placeholder - would need more sophisticated method)
                confidences[model_type] = 0.7  # Base confidence
            
            if not predictions:
                return None
            
            # Ensemble prediction (weighted average)
            weights = {
                'lstm_deep': 0.3,
                'xgboost': 0.25,
                'lightgbm': 0.2,
                'random_forest': 0.15,
                'gradient_boosting': 0.1
            }
            
            ensemble_pred = 0
            total_weight = 0
            
            for model_type, pred in predictions.items():
                weight = weights.get(model_type, 0.1)
                ensemble_pred += pred * weight
                total_weight += weight
            
            if total_weight > 0:
                ensemble_pred /= total_weight
            
            # Calculate ensemble confidence
            pred_values = list(predictions.values())
            pred_std = np.std(pred_values)
            ensemble_confidence = max(0.1, 1.0 - (pred_std / np.mean(pred_values)))
            
            return {
                'ensemble_prediction': ensemble_pred,
                'ensemble_confidence': ensemble_confidence,
                'individual_predictions': predictions,
                'individual_confidences': confidences,
                'horizon_minutes': horizon_minutes,
                'timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error making ensemble prediction for {symbol}: {e}")
            return None
    
    async def _calculate_prediction_accuracy(self, symbol: str) -> Dict:
        """Calculate prediction accuracy metrics"""
        try:
            # Get predictions from last 24 hours
            cutoff_date = datetime.utcnow() - timedelta(hours=24)
            
            predictions_query = """
            SELECT * FROM ml_predictions 
            WHERE symbol = $1 AND timestamp > $2
            ORDER BY timestamp DESC
            """
            
            predictions = await self.db_manager.fetch_all(predictions_query, symbol, cutoff_date)
            
            if len(predictions) < 10:
                return {}
            
            # Get actual prices for comparison
            accuracies = {}
            
            for pred in predictions:
                pred_time = pred['timestamp']
                horizon_minutes = pred['horizon_minutes']
                predicted_price = pred['ensemble_prediction']
                
                # Get actual price at prediction target time
                target_time = pred_time + timedelta(minutes=horizon_minutes)
                
                actual_query = """
                SELECT close_price FROM market_data 
                WHERE symbol = $1 AND timestamp >= $2 
                ORDER BY timestamp ASC LIMIT 1
                """
                
                actual_result = await self.db_manager.fetch_one(actual_query, symbol, target_time)
                
                if actual_result:
                    actual_price = actual_result['close_price']
                    error_pct = abs(predicted_price - actual_price) / actual_price * 100
                    
                    horizon_key = f"{horizon_minutes}min"
                    if horizon_key not in accuracies:
                        accuracies[horizon_key] = []
                    
                    accuracies[horizon_key].append(error_pct)
            
            # Calculate accuracy metrics
            performance_metrics = {
                'symbol': symbol,
                'evaluation_time': datetime.utcnow(),
                'sample_size': len(predictions)
            }
            
            for horizon, errors in accuracies.items():
                if errors:
                    mean_error = np.mean(errors)
                    accuracy = max(0, 100 - mean_error)  # Convert error to accuracy
                    
                    performance_metrics[f'{horizon}_accuracy'] = accuracy
                    performance_metrics[f'{horizon}_mean_error'] = mean_error
                    performance_metrics[f'{horizon}_std_error'] = np.std(errors)
            
            # Overall ensemble accuracy
            all_errors = [error for errors in accuracies.values() for error in errors]
            if all_errors:
                performance_metrics['ensemble_accuracy'] = max(0, 100 - np.mean(all_errors))
            
            return performance_metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating prediction accuracy for {symbol}: {e}")
            return {}
    
    async def _hyperparameter_optimization(self, symbol: str):
        """Optimize model hyperparameters"""
        try:
            # Get training data
            training_data = await self._prepare_training_data(symbol)
            
            if training_data is None or len(training_data) < 100:
                return
            
            # Focus on best performing models for optimization
            models_to_optimize = ['xgboost', 'lightgbm']
            
            for model_type in models_to_optimize:
                await self._optimize_single_model(symbol, model_type, training_data)
                
        except Exception as e:
            self.logger.error(f"Error optimizing hyperparameters for {symbol}: {e}")
    
    async def _optimize_single_model(self, symbol: str, model_type: str, training_data: pd.DataFrame):
        """Optimize a single model's hyperparameters"""
        try:
            # Prepare data
            feature_cols = [col for col in training_data.columns if not col.startswith('target_')]
            X = training_data[feature_cols].values
            y = training_data['target_medium_term'].values
            
            # Remove NaN values
            valid_idx = ~np.isnan(y)
            X = X[valid_idx]
            y = y[valid_idx]
            
            if len(X) < 50:
                return
            
            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42
            )
            
            best_score = float('-inf')
            best_params = None
            
            # Define parameter grids
            if model_type == 'xgboost':
                param_grid = {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [3, 6, 9],
                    'learning_rate': [0.01, 0.1, 0.2]
                }
            elif model_type == 'lightgbm':
                param_grid = {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [3, 6, 9],
                    'learning_rate': [0.01, 0.1, 0.2]
                }
            else:
                return
            
            # Simple grid search (in production, consider using optuna or similar)
            for params in self._generate_param_combinations(param_grid):
                try:
                    if model_type == 'xgboost':
                        model = xgb.XGBRegressor(**params, random_state=42)
                    elif model_type == 'lightgbm':
                        model = lgb.LGBMRegressor(**params, random_state=42, verbosity=-1)
                    
                    # Cross-validation score
                    cv_scores = cross_val_score(model, X_train, y_train, cv=3, 
                                              scoring='neg_mean_squared_error')
                    score = cv_scores.mean()
                    
                    if score > best_score:
                        best_score = score
                        best_params = params
                        
                except Exception as e:
                    continue
            
            # Update model with best parameters
            if best_params:
                if model_type == 'xgboost':
                    self.models[symbol][model_type] = xgb.XGBRegressor(**best_params, random_state=42)
                elif model_type == 'lightgbm':
                    self.models[symbol][model_type] = lgb.LGBMRegressor(**best_params, random_state=42, verbosity=-1)
                
                self.scalers[symbol][model_type] = scaler
                
                # Retrain with best parameters
                self.models[symbol][model_type].fit(X_train, y_train)
                
                self.logger.info(f"Optimized {model_type} for {symbol} with params: {best_params}")
                
        except Exception as e:
            self.logger.error(f"Error optimizing {model_type} for {symbol}: {e}")
    
    def _generate_param_combinations(self, param_grid: Dict) -> List[Dict]:
        """Generate parameter combinations for grid search"""
        keys = list(param_grid.keys())
        combinations = []
        
        def generate_recursive(params, key_idx):
            if key_idx == len(keys):
                combinations.append(params.copy())
                return
            
            key = keys[key_idx]
            for value in param_grid[key]:
                params[key] = value
                generate_recursive(params, key_idx + 1)
        
        generate_recursive({}, 0)
        return combinations[:20]  # Limit combinations to avoid too long optimization
    
    async def _generate_trading_signals(self, symbol: str) -> Optional[Dict]:
        """Generate trading signals based on predictions"""
        try:
            # Get latest predictions
            predictions_query = """
            SELECT * FROM ml_predictions 
            WHERE symbol = $1 
            ORDER BY timestamp DESC 
            LIMIT 4
            """
            
            predictions = await self.db_manager.fetch_all(predictions_query, symbol)
            
            if len(predictions) < 2:
                return None
            
            # Get current price
            current_price_query = """
            SELECT close_price FROM market_data 
            WHERE symbol = $1 
            ORDER BY timestamp DESC 
            LIMIT 1
            """
            
            current_price_result = await self.db_manager.fetch_one(current_price_query, symbol)
            
            if not current_price_result:
                return None
            
            current_price = current_price_result['close_price']
            
            # Analyze predictions for signal generation
            latest_pred = predictions[0]
            predicted_price = latest_pred['ensemble_prediction']
            confidence = latest_pred['ensemble_confidence']
            
            # Calculate expected return
            expected_return = (predicted_price - current_price) / current_price * 100
            
            # Generate signal based on prediction and confidence
            signal_strength = 0
            signal_direction = 'HOLD'
            
            if confidence > 0.6:  # High confidence threshold
                if expected_return > 1.0:  # Expect >1% gain
                    signal_direction = 'BUY'
                    signal_strength = min(1.0, confidence * (expected_return / 2))
                elif expected_return < -1.0:  # Expect >1% loss
                    signal_direction = 'SELL'
                    signal_strength = min(1.0, confidence * (abs(expected_return) / 2))
            
            # Calculate risk metrics
            price_volatility = await self._calculate_price_volatility(symbol)
            prediction_consistency = await self._calculate_prediction_consistency(symbol)
            
            return {
                'symbol': symbol,
                'signal_direction': signal_direction,
                'signal_strength': signal_strength,
                'expected_return': expected_return,
                'confidence': confidence,
                'current_price': current_price,
                'predicted_price': predicted_price,
                'price_volatility': price_volatility,
                'prediction_consistency': prediction_consistency,
                'timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating trading signals for {symbol}: {e}")
            return None
    
    async def _calculate_price_volatility(self, symbol: str) -> float:
        """Calculate recent price volatility"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(hours=24)
            
            query = """
            SELECT close_price FROM market_data 
            WHERE symbol = $1 AND timestamp > $2 
            ORDER BY timestamp ASC
            """
            
            prices = await self.db_manager.fetch_all(query, symbol, cutoff_date)
            
            if len(prices) < 10:
                return 0.0
            
            price_values = [p['close_price'] for p in prices]
            returns = np.diff(np.log(price_values))
            
            return float(np.std(returns) * np.sqrt(24))  # Annualized volatility
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility for {symbol}: {e}")
            return 0.0
    
    async def _calculate_prediction_consistency(self, symbol: str) -> float:
        """Calculate consistency of recent predictions"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(hours=6)
            
            query = """
            SELECT ensemble_prediction FROM ml_predictions 
            WHERE symbol = $1 AND timestamp > $2 
            ORDER BY timestamp DESC
            """
            
            predictions = await self.db_manager.fetch_all(query, symbol, cutoff_date)
            
            if len(predictions) < 3:
                return 0.5
            
            pred_values = [p['ensemble_prediction'] for p in predictions]
            
            # Calculate coefficient of variation
            mean_pred = np.mean(pred_values)
            std_pred = np.std(pred_values)
            
            if mean_pred != 0:
                cv = std_pred / mean_pred
                consistency = max(0, 1 - cv)  # Lower CV = higher consistency
            else:
                consistency = 0.5
            
            return float(consistency)
            
        except Exception as e:
            self.logger.error(f"Error calculating prediction consistency for {symbol}: {e}")
            return 0.5
    
    async def _store_predictions(self, symbol: str, predictions: Dict):
        """Store ML predictions in database"""
        for horizon_name, prediction_data in predictions.items():
            if prediction_data:
                query = """
                INSERT INTO ml_predictions (symbol, horizon_name, horizon_minutes, ensemble_prediction,
                                          ensemble_confidence, individual_predictions, timestamp)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """
                
                await self.db_manager.execute(
                    query,
                    symbol, horizon_name, prediction_data['horizon_minutes'],
                    prediction_data['ensemble_prediction'], prediction_data['ensemble_confidence'],
                    str(prediction_data['individual_predictions']), prediction_data['timestamp']
                )
    
    async def _store_model_performance(self, symbol: str, metrics: Dict):
        """Store model performance metrics"""
        query = """
        INSERT INTO ml_model_performance (symbol, evaluation_time, sample_size, ensemble_accuracy,
                                        performance_metrics)
        VALUES ($1, $2, $3, $4, $5)
        """
        
        await self.db_manager.execute(
            query,
            symbol, metrics['evaluation_time'], metrics['sample_size'],
            metrics.get('ensemble_accuracy', 0), str(metrics)
        )
    
    async def _store_trading_signals(self, symbol: str, signals: Dict):
        """Store trading signals"""
        query = """
        INSERT INTO ml_trading_signals (symbol, signal_direction, signal_strength, expected_return,
                                      confidence, current_price, predicted_price, price_volatility,
                                      prediction_consistency, timestamp)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        """
        
        await self.db_manager.execute(
            query,
            signals['symbol'], signals['signal_direction'], signals['signal_strength'],
            signals['expected_return'], signals['confidence'], signals['current_price'],
            signals['predicted_price'], signals['price_volatility'],
            signals['prediction_consistency'], signals['timestamp']
        )
    
    async def get_prediction_summary(self, symbol: str) -> Dict:
        """Get prediction summary for a symbol"""
        query = """
        SELECT horizon_name, ensemble_prediction, ensemble_confidence, timestamp
        FROM ml_predictions 
        WHERE symbol = $1 
        ORDER BY timestamp DESC 
        LIMIT 4
        """
        
        predictions = await self.db_manager.fetch_all(query, symbol)
        
        # Get latest trading signal
        signal_query = """
        SELECT * FROM ml_trading_signals 
        WHERE symbol = $1 
        ORDER BY timestamp DESC 
        LIMIT 1
        """
        
        signal = await self.db_manager.fetch_one(signal_query, symbol)
        
        return {
            'symbol': symbol,
            'predictions': [dict(pred) for pred in predictions],
            'latest_signal': dict(signal) if signal else None
        }


# Alias for backward compatibility
MarketPredictor = MLMarketPredictor
